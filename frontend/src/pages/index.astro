---
import Site from '../layouts/Site.astro'
import Gallery from '../components/container/Gallery.astro'
import Link from '../components/elements/Link.astro'
import FaqItem from '../components/patterns/FaqItem.astro'

import { Var } from '../script/var'
import { currentPage, currentTopic, currentSubTopic } from '../script/store'
currentPage.set('home')
currentTopic.set('')
currentSubTopic.set('')

const title = `${Var.app.name} — ${Var.app.tagline}`
const description = Var.app.description
---

<Site title={title} description={description}>
    <section class="logo-section">
      <div class="logo-container">
        <img
          src="https://res.cloudinary.com/dyygc6dx2/image/upload/v1743453235/logotype_pxbcf5.svg"
          alt="SenseFolks Logo"
          class="sensefolks-logo"
          width="225"
          height="59"
        />
        <p class="footnote logo-footnote">
          Made by <Link href="https://projckt.com" theme="default" target="_blank">Projckt</Link>
        </p>
      </div>
    </section>
    <section class="hero-section spacing__vertical--900">
      <div class="spacing__vertical--900 hero-container">
        <h1 class="hero-header">Read your customers' mind</h1>
        <div class="spacing__vertical--50"></div>
        <h2>Micro-surveys that help you <strong>make epic product decisions</strong></h2>
        <div class="dot-grid hero-dot-separator"></div>
        <!-- <div class="spacing__vertical--200"></div>
        <div class="hero__buttons spacing__vertical--100">
          <Link href="#surveys" variant="button" fill="outline" theme="default">
            <strong>Explore Surveys</strong>
          </Link>
          &nbsp;&nbsp;
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Get Started</strong>
          </Link>
        </div> -->
        <div class="spacing__vertical--400"></div>
        <Gallery>
          <div class="gallery__item">
            <img
              class="benefits__icons"
              src="./target.svg"
              width="56"
              height="56"
              alt="SenseFolks surveys gather feedback about a specific aspect of the product"
            />
            <h3>Hyper Focused</h3>
            <p>Each survey measures one thing extremely well</p>
          </div>
          <div class="gallery__item">
            <img
              class="benefits__icons"
              src="./wind.svg"
              width="56"
              height="56"
              alt="SenseFolks surveys use proven research methods for accurate insights"
            />
            <h3>Effortless</h3>
            <p>Surveys that does not interrupt user experience</p>
          </div>
          <div class="gallery__item">
            <img
              class="benefits__icons"
              src="./bolt.svg"
              width="56"
              height="56"
              alt="No need for customisations. Embed SenseFolks surveys on your website"
            />
            <h3>Quick Setup</h3>
            <p>Create & embed surveys on your website in 2 minutes</p>
          </div>
        </Gallery>
      </div>
      
    </section>
    <section class="spacing__vertical--1200 dashboard-section full-width-section">

    </section>
    <section id="surveys" class="spacing__vertical--1600">
      <article>
        <h1>The Surveys</h1>
        <div class="spacing__vertical--50"></div>
        <h2>Designed with intent and purpose</h2>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title">SensePrice</h3>
            <div class="spacing__vertical--50"></div>
            <h3>Discover how much your customers are willing to pay</h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  Eliminate guesswork from pricing
                </p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  Confidently set the optimal price
                </p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Don't leave money on the table</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3>SensePriority</h3>
            <div class="spacing__vertical--50"></div>
            <h3>
              Prioritize features that drive customer satisfaction and retention
            </h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Identify high-impact features</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Enhance product-market fit</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Improve your ROI</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3>SenseChoice</h3>
            <div class="spacing__vertical--50"></div>
            <h3>
              Prioritize features that drive customer satisfaction and retention
            </h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Identify high-impact features</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Enhance product-market fit</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Improve your ROI</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3>SensePoll</h3>
            <div class="spacing__vertical--50"></div>
            <h3>Create highly flexible polls to capture opinions, preferences, and feedback</h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Use single or multi-choice questions</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Get clear & structured insights</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Identify trends & optimize your decisions</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3>SenseQuery</h3>
            <div class="spacing__vertical--50"></div>
            <h3>Uncover unanswered questions and identify what your audience wants to know</h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Spot knowledge or content gaps</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Enhance clarity</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Eliminate confusion</p>
              </li>
            </ul>
          </div>
        </div>
      </article>
    </section>
    <!-- <section class="spacing__vertical--900">
      <article>
        <div class="cta__container">
          <h2 class="spacing__vertical--100">
            Get product feedback that matters
          </h2>
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Get Started</strong>
          </Link>
        </div>
      </article>
    </section> -->
    <section class="spacing__vertical--1600 dashboard-section full-width-section">
      <article>
        <h1>The Dashboard</h1>
        <div class="spacing__vertical--50"></div>
        <h2>Everything in one place</h2>
        <div class="spacing__vertical--900">
          <div class="dashboard__thumbnail"></div>
          <div class="dashboard__benefit">
            <ul
              class="no-margin--left row row__justify--space-between row--wrap no-margin--bottom"
            >
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Create & organize surveys</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Get automated insights</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Make smart decisions</strong>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div class="spacing__vertical--900">
          <div class="dashboard__thumbnail"></div>
          <div class="dashboard__benefit">
            <ul
              class="no-margin--left row row__justify--space-between row--wrap no-margin--bottom"
            >
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Get insights by country</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Spot regional preferences</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Optimize regional strategies</strong>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div class="spacing__vertical--900">
          <div class="dashboard__thumbnail"></div>
          <div class="dashboard__benefit no-margin--bottom">
            <ul
              class="no-margin--left row row__justify--space-between row--wrap no-margin--bottom"
            >
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Define your personas</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Get insights by persona</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Optimize offerings for personas</strong>
                </p>
              </li>
            </ul>
          </div>
        </div>
      </article>
    </section>
    <!-- <section class="spacing__vertical--900">
      <article>
        <div class="cta__container">
          <h2 class="spacing__vertical--100">
            Get automated analysis & insights
          </h2>
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Sign Up</strong>
          </Link>
        </div>
      </article>
    </section> -->
    <section class="spacing__vertical--600">
      <article>
        <h1 class="spacing__vertical--100">FAQs</h1>
        <div class="content--narrow">
          <FaqItem question="What is SenseFolks?">
              <p class="spacing__vertical--50">
                SenseFolks is an all-in-one platform designed to help you
                understand your customers better. We offer a suite of survey
                tools that allow you to gather valuable insights on pricing,
                features, branding, content, and customer engagement. Our goal
                is to make it easy for you to make informed decisions and build
                products that people love.
              </p>
            </FaqItem>
            <div class="spacing__vertical--150"></div>
            <FaqItem question="How is it different from other surveys?">
              <p class="spacing__vertical--50">
                SenseFolks stands out because we offer ready-made surveys
                designed for specific purposes that are based on established
                research methods. Unlike generic survey tools, you don't have to
                spend time customizing forms and figuring out how to analyze the
                results. SenseFolks automatically processes and analyzes the
                data, saving you time and efforts on data formatting and
                calculations, so you can focus on making informed decisions
                right away.
              </p>
            </FaqItem>
            <div class="spacing__vertical--150"></div>
            <FaqItem question="Can it help me make better decisions?">
              <p class="spacing__vertical--50">
                Definitely! The core purpose of SenseFolks is to provide you
                with the insights needed to make informed, data-driven
                decisions. Whether you’re setting a price, choosing new features
                to develop, or refining your brand strategy, our surveys give
                you the information you need to make decisions with confidence.
              </p>
            </FaqItem>
            <div class="spacing__vertical--150"></div>
            <FaqItem question="Who can benefit from SenseFolks?">
              <p class="spacing__vertical--50">
                SenseFolks can benefit businesses of all sizes and industries.
                Whether you’re a startup looking to validate your product idea,
                an established company aiming to improve customer satisfaction
                or an independent author trying to set the price of your
                upcoming book, we can offer valuable insights to help you
                succeed.
              </p>
            </FaqItem>
            <div class="spacing__vertical--150"></div>
            <!-- <span class="footnote">
              <Link href=`${Var.app.website.url}/faq/` theme="default" target="_blank">
              READ MORE FAQs
              </Link>
            </span> -->

        </div>
      </article>
    </section>
    <div class="separator spacing__vertical--300"></div>
    <section class="spacing__vertical--200">
      <article>
        <div class="copyright-section">
          <p class="footnote">© 2024 SenseFolks. All rights reserved.</p>
          <p class="footnote">Made by <Link href="https://projckt.com" theme="default" target="_blank">Projckt</Link></p>
        </div>
      </article>
    </section>
    <!-- <section class="spacing__vertical--600">
      <article>
        <div class="cta__container">
          <h2 class="spacing__vertical--100">
            Drastically improve your product
          </h2>
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Get Started</strong>
          </Link>
        </div>
      </article>
    </section> -->
</Site>

<style>
  body {
    width: 100%;
    max-width: none;
  }

  article {
    width: 870px;
    margin: 0 auto;
  }

  section {
    text-align: center;
    max-width: 1024px;
    margin: 0 auto;
  }

  .full-width-section {
    max-width: none;
    width: 100%;
  }

  .dashboard__benefit {
    text-align: left;
  }

  .dashboard__benefit .row {
    align-items: center;
  }

  .dashboard-section {
    background: linear-gradient(135deg, var(--color__indigo--900) 0%, var(--color__indigo--700) 100%);
    padding: calc(var(--padding) * 4 + 5em) calc(var(--padding) * 4) calc(var(--padding) * 4) calc(var(--padding) * 4);
    position: relative;
  }

  .dashboard-section h1 {
    color: var(--color__white);
  }

  .dashboard-section h2 {
    color: var(--color__indigo--50);
  }

  .dashboard-section p {
    color: var(--color__white);
  }



  .dot-separator {
    height: 100px;
    width: 100%;
  }

  .hero-dot-separator {
    width: 100%;
    aspect-ratio: 16 / 9;
    margin-top: 3em;
  }

  .spacing__vertical--1600 {
    margin: 16em 0;
  }

  .copyright-section {
    text-align: center;
    margin-bottom: 2em;
  }

  .copyright-section p {
    margin: 0.5em 0;
    color: var(--color__grey--500);
  }

  .hero-header {
    font-size: 3.5em;
    line-height: 1
  }

  .logo-section {
    display: flex;
    justify-content: center;
    align-items: center;
    /* padding: 4em 0 2em 0;
     */
     margin-top: 4em;
  }

  .logo-container {
    text-align: center;
  }

  .sensefolks-logo {
    max-width: 100%;
    height: auto;
  }

  .logo-footnote {
    margin-bottom: 0;
    text-align: right;
  }

  .hero-section {
    margin-top: 3em;
  }

  .content--narrow {
    width: 560px;
    text-align: left;
    margin: 0 auto;
  }

  .gallery__item {
    width: 300px;
    text-align: center;
    background: var(--color__white);
    padding: calc(var(--padding) * 2);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(63, 81, 181, 0.15);
    border: 1px solid rgba(63, 81, 181, 0.1);
  }

  .cta__container {
    color: var(--color__grey--800);
    border: 1px solid var(--color__grey--800);
    background: var(--color__grey--50);
    padding: var(--padding) var(--padding) calc(3 * var(--padding)) var(--padding);
    border-radius: calc(var(--border-radius) * 2);
  }

  .cta__container h2 {
    color: var(--color__grey--700);
  }

  .survey__benefits .row {
    align-items: center;
  }

  .survey__preview__bg {
    height: 400px;
    width: 50%;

  }

  .survey__thumbnail__description {
    width: 45%;
    text-align: left;
  }

  .survey__thumbnail__description h2 {
    margin-bottom: 0.5em;
  }

  .dashboard__thumbnail {
    display: flex;
    justify-content: space-around;
    align-items: center;
    border: 1px solid var(--color__indigo--800);
    height: 500px;
    border-radius: var(--border-radius);
    width: 100%;
  }

  .masonry {
    display: flex; flex-wrap: wrap; gap: 40px; justify-content: space-around;
  }

  .masonry-row {
    display: flex;
    align-items: center;
  }

  @media only screen and (max-width: 480px) {
    body {
      width: 90%;
      margin: 0 auto;
    }

    article {
      width: 100%;
      text-align: left;
    }

    section {
      text-align: left;
    }

    .gallery__item {
      width: 100%;
      text-align: left;
      padding: calc(var(--padding) * 1.5);
      margin-bottom: calc(var(--padding) * 1.5);
    }

    .hero__buttons {
      display: flex;
    }

    .logo-section {
      justify-content: flex-start;
      /*
      padding: 2em 0 1em 0; */
      margin-top: 2em;
    }

    .logo-container {
      text-align: left;
    }

    .sensefolks-logo {
      width: 188px;
      max-width: 90vw;
    }

    .logo-footnote {
      text-align: right;
    }

      .hero-header {
        font-size: 3em;
      }

    .benefits__row {
      display: block;
    }

    .masonry {
      display: block;
    }

    .masonry-row {
      align-items: baseline;
      margin-bottom: 2em;
    }

    .dashboard__benefit .row {
      align-items: baseline;
    }

    .content--narrow {
      width: 100%;
    }

    .survey__benefits .row,
    .dashboard__benefit .row {
      display: flex;
      align-items: baseline;
    }

    .survey__preview__bg {
      width: 100%;
    }

    .survey__thumbnail__description {
      width: 100%;
    }

    .cta__container {
      padding: calc(var(--padding) * 1.5);
    }

    .material-symbols-outlined {
      z-index: 9;
    }

    .dashboard-section {
      padding: calc(var(--padding) * 2 + 2em) calc(var(--padding) * 2) calc(var(--padding) * 2) calc(var(--padding) * 2);
    }
  }
</style>

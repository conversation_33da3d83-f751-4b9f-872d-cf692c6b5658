---
interface Props {
  question: string
}

const { question } = Astro.props
---

<details>
  <summary>
    {question}
  </summary>
  <p>
    <slot />
  </p>
</details>

<style>
  details {
    display: block;
    border-radius: var(--border-radius);
  }

  summary {
    text-decoration: none;
    color: var(--color__indigo--500);
    transition: all 0.25s;
  }

  summary:hover {
    color: var(--color__indigo--700);
    cursor: pointer;
  }

  details:hover {
    cursor: pointer;
  }
</style>

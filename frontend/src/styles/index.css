@import 'reset.css';
/* @import 'vars--dark.css'; */
@import 'vars--light.css';

@font-face {
  font-family: 'Work Sans';
  src: url('/WorkSans-Regular.woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Space Grotesk';
  src: url('/SpaceGrotesk.woff2');
  font-display: swap;
}

html {
  background: var(--color__bg);
  min-height: 100vh;
}

body {
  font-size: var(--font-size__body);
  color: var(--color__grey--700);
  font-family: var(--font-family__body);
  margin: 0 auto;
  width: 95vw;
  max-width: 1024px;
  line-height: 1.5;
}

h1,
h2,
h3 {
  color: var(--color__grey--600);
  font-family: var(--font-family__heading);
}

h1 {
  font-size: 2.5em;
  line-height: 1.15;
  font-family: var(--font-family__heading);
}

h2 {
  color: var(--color__grey--500);
  font-weight: 400;
  font-size: 1.75em;
  line-height: 1.3;
}

h3 {
  font-size: 1.2em;
  line-height: 1.5;
}

ul,
ol {
  list-style-position: otside;
  margin-left: 1em;
}

section {
  max-width: 640px;
}

.text--light {
  color: var(--color__grey--500);
}

.footnote {
  font-size: 0.8em;
  color: var(--color__grey--700);
}

.list--no-bullet {
  list-style-type: none;
}

.no-margin--top {
  margin-top: 0;
}

.no-margin--right {
  margin-right: 0;
}

.no-margin--bottom {
  margin-bottom: 0;
}

.no-margin--left {
  margin-left: 0;
}

.row {
  display: flex;
  align-items: center;
}

.row__justify--space-between {
  justify-content: space-between;
}

.row__align--baseline {
  align-items: baseline;
}

.row__align--center {
  align-items: center;
}

.row--wrap {
  flex-wrap: wrap;
}

.separator {
  border-bottom: 1px solid var(--color__grey--200);
}

.material-symbols-outlined {
  margin-right: 0.25em;
}

.material-symbols-outlined--large {
  width: 30px;
  margin-right: 0em;
}

.display-on-desktop {
  display: block;
}

.display-on-tablet {
  display: none;
}

.display-on-mobile {
  display: none;
}

.spacing__horizontal--50 {
  margin: 0 0.5em;
}

.spacing__horizontal--100 {
  margin: 0 1em;
}

.spacing__horizontal--200 {
  margin: 0 2em;
}

.spacing__horizontal--300 {
  margin: 0 3em;
}

.spacing__horizontal--400 {
  margin: 0 4em;
}

.spacing__vertical--50 {
  margin: 0.5em 0;
}

.spacing__vertical--100 {
  margin: 1em 0;
}

.spacing__vertical--150 {
  margin: 1.5em 0;
}

.spacing__vertical--200 {
  margin: 2em 0;
}

.spacing__vertical--300 {
  margin: 3em 0;
}

.spacing__vertical--400 {
  margin: 4em 0;
}

.spacing__vertical--500 {
  margin: 5em 0;
}

.spacing__vertical--600 {
  margin: 6em 0;
}

.spacing__vertical--700 {
  margin: 7em 0;
}

.spacing__vertical--800 {
  margin: 8em 0;
}

.spacing__vertical--900 {
  margin: 9em 0;
}

.dot-grid {
  --dot-bg: var(--color__bg);
  --dot-color: var(--color__grey--400);
  --dot-size: 2px;
  --dot-space: 22px;
  background:
    linear-gradient(
        90deg,
        var(--dot-bg) calc(var(--dot-space) - var(--dot-size)),
        transparent 1%
      )
      center / var(--dot-space) var(--dot-space),
    linear-gradient(
        var(--dot-bg) calc(var(--dot-space) - var(--dot-size)),
        transparent 1%
      )
      center / var(--dot-space) var(--dot-space),
    var(--dot-color);
}

@media only screen and (max-width: 480px) {
  .display-on-desktop {
    display: none;
  }

  .display-on-tablet {
    display: none;
  }

  .display-on-mobile {
    display: block;
  }

  .material-symbols-outlined {
    position: relative;
    top: 10px;
    z-index: -9;
  }

  .row {
    align-items: baseline;
    display: block;
  }

  body {
    width: 90%;
    margin: 0 auto;
    font-size: 20px;
  }

  h1 {
    font-size: 1.8em;
    line-height: 1.15;
  }

  h2 {
    font-weight: 400;
    font-size: 1.5em;
    line-height: 1.3;
  }

  h3 {
    font-size: 1.15em;
    line-height: 1.5;
  }
}
